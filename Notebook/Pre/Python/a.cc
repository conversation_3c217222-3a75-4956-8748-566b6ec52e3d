#include <iostream>
#include <vector>
#include <cmath>
#include <random>
#include <chrono>

// 要积分的函数
double f(double x) {
    return std::log(1.0 - x);
}

// 使用蒙特卡洛方法计算积分
double monte_carlo_integrate(double a, double b, long long n_samples) {
    // 1. 设置高质量的随机数生成器
    // 使用当前时间作为种子，或者使用 random_device 以获得更好的随机性
    unsigned seed = std::chrono::high_resolution_clock::now().time_since_epoch().count();
    // 如果需要和Python结果完全一致，可以使用固定种子，例如 std::mt19937 gen(42);
    std::mt19937 gen(seed); 
    std::uniform_real_distribution<double> dis(a, b);

    double sum_of_values = 0.0;

    // 2. 循环生成随机点并累加函数值
    for (long long i = 0; i < n_samples; ++i) {
        double random_point = dis(gen);
        sum_of_values += f(random_point);
    }
    
    // 3. 计算函数值的平均值
    double average_value = sum_of_values / n_samples;

    // 4. 估算积分值
    return (b - a) * average_value;
}

int main() {
    // 定义积分区间和采样点数
    const double integration_min = 0.0;
    const double integration_max = 1.0; // 避开奇点
    const long long num_samples = 500000000;

    // 执行积分
    double result = monte_carlo_integrate(integration_min, integration_max, num_samples);
    
    std::cout << "C++ Implementation" << std::endl;
    std::cout << "Integration interval: [" << integration_min << ", " << integration_max << "]" << std::endl;
    std::cout << "Number of samples: " << num_samples << std::endl;
    
    // 设置输出精度
    std::cout.precision(7);
    std::cout << std::fixed;
    
    std::cout << "Monte Carlo estimate: " << result << std::endl;
    std::cout << "Analytical solution:  -0.8002135" << std::endl;

    return 0;
}