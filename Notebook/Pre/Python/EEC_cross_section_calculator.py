# 所需的QCD模块(自己编写的模块)
from ALZ_running import AlphaSRunner
from phase_space_zjet import PhaseSpaceZJet
from evolution_matrix import EvolutionMatrix


import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
from typing import Tuple


# 定义一个进程本地缓存，用于安全地在多进程环境中初始化和存储LHAPDF对象。
_PDF_CACHE = {}

# 防止Vegas多线程环境下出现问题
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"


class ZJetEECVegasIntegrand:
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4]

    def __init__(
        self,
        pdf_name: str,
        alphas_runner: AlphaSRunner,
        Ecom: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        radius: float,
        muR_factor: float = 1.0,
        muF_factor: float = 1.0,
    ):
        """
        初始化Z+jet EEC积分被积函数

        Args:
            pdf_name: PDF集合名称
            alphas_runner: 强耦合常数运行器
            pT_range: 横动量范围 (pT_min, pT_max)
            eta_range: 赝快度范围 (eta_min, eta_max)
            radius: 喷注半径参数
            muR_factor: 重整化标度因子 κ_R
            muF_factor: 因子化标度因子 κ_F
        """
        self.pdf_name = pdf_name
        self.alphas_runner = alphas_runner
        self.Ecom = Ecom
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.radius = radius
        self.muR_factor = muR_factor
        self.muF_factor = muF_factor

        # 初始化不同阶的EEC演化矩阵
        self.eec_evolver_leading_log = EvolutionMatrix(
            order=0, kappa=self.muF_factor, ALZ_runner=alphas_runner
        )
        self.eec_evolver_next_to_leading_log = EvolutionMatrix(
            order=1, kappa=self.muF_factor, ALZ_runner=alphas_runner
        )
        self.eec_evolver_next_to_next_leading_log = EvolutionMatrix(
            order=2, kappa=self.muF_factor, ALZ_runner=alphas_runner
        )

        # 初始化相空间计算器
        self.phase_space = PhaseSpaceZJet(Ecom=Ecom)
        self.z_boson_mass = self.phase_space.MZ
        self.z_boson_width = self.phase_space.GZ

    def __call__(self, integration_variables: np.ndarray) -> np.ndarray:
        """
        Vegas积分被积函数调用接口

        Args:
            integration_variables: 积分变量数组，形状为 (N, 4)
                                  [y_pT, y_eta, y_v, y_zc]

        Returns:
            积分结果数组，形状为 (N, 16)
        """

        # 获取进程ID, 并在必要时初始化PDF对象
        process_id = os.getpid()
        if process_id not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[process_id] = lhapdf.mkPDF(self.pdf_name)
        pdf_object = _PDF_CACHE[process_id]

        integration_variables = np.atleast_2d(integration_variables)
        num_events = integration_variables.shape[0]

        output_array = np.empty((num_events, 16), dtype=float)

        for event_index in range(num_events):
            output_array[event_index] = self._single_event(
                integration_variables[event_index, 0],  # y_pT
                integration_variables[event_index, 1],  # y_eta
                integration_variables[event_index, 2],  # y_v
                integration_variables[event_index, 3],  # y_zc
                pdf_object,
            )

        return output_array

    def _LO_dsigma(self, pT, eta, v, zc, pdf_object):

        # 计算重整化和因子化标度
        muR = self.muR_factor * pT
        muF = self.muF_factor * pT

        # 计算不同能标下的强耦合常数
        alphas_muR = self.alphas_runner.alphas(muR)

        # 只考虑LO截面
        w = 1.0

        x1 = self.phase_space.x1(v, w, zc, pT, eta)
        x2 = self.phase_space.x2(v, w, zc, pT, eta)
        if not (0 < x1 < 1 and 0 < x2 < 1):
            return 0.0

        shat = self.phase_space.shat(v, w, zc, pT, eta)
        uhat = self.phase_space.u(pT, eta) * x2 / zc
        that = self.phase_space.t(pT, eta) * x1 / zc
        if uhat == 0 or that == 0 or shat <= 0:
            return 0.0

        # 计算光度
        parton_luminosity_qg_zq = 0.0
        parton_luminosity_qqbar_zg = 0.0
        for quark_flavor in self.QUARK_FLAVORS_TO_SUM:
            # 获取夸克和胶子的PDF ID
            quark_pdg_id = quark_flavor
            gluon_pdg_id = 21

            # 获取夸克与Z玻色子的电弱耦合系数
            z_boson_coupling = self.phase_space.vq_plus_aq(quark_flavor)

            # 从PDF对象中获取在标度muF和x1动量分数处的PDF值
            pdf_quark_x1 = pdf_object.xfxQ(quark_pdg_id, x1, muF)
            pdf_antiquark_x1 = pdf_object.xfxQ(-quark_pdg_id, x1, muF)
            pdf_gluon_x1 = pdf_object.xfxQ(gluon_pdg_id, x1, muF)

            # 从PDF对象中获取在标度muF和x2动量分数处的PDF值
            pdf_quark_x2 = pdf_object.xfxQ(quark_pdg_id, x2, muF)
            pdf_antiquark_x2 = pdf_object.xfxQ(-quark_pdg_id, x2, muF)
            pdf_gluon_x2 = pdf_object.xfxQ(gluon_pdg_id, x2, muF)

            # 计算并累加 qg -> Zq 和 gq -> Zq 过程的光度
            parton_luminosity_qg_zq += (
                (pdf_quark_x1 + pdf_antiquark_x1) * pdf_gluon_x2
                + pdf_gluon_x1 * (pdf_quark_x2 + pdf_antiquark_x2)
            ) * z_boson_coupling

            # 计算并累加 qqbar -> Zg 过程的光度
            parton_luminosity_qqbar_zg += (
                pdf_quark_x1 * pdf_antiquark_x2 + pdf_antiquark_x1 * pdf_quark_x2
            ) * z_boson_coupling**2

        # 计算散射矩阵元
        me_qg_num = shat**2 + uhat**2 - 2.0 * self.z_boson_mass**2 * that
        matrix_element_qg = -1.0 / 12.0 * (me_qg_num / (shat * uhat))

        me_qqbar_num = that**2 + uhat**2 + 2.0 * self.z_boson_mass**2 * shat
        matrix_element_qqbar = 2.0 / 9.0 * (me_qqbar_num / (that * uhat))

        # 计算微分截面
        common_factor = (
            (alphas_muR / (8.0 * shat))
            * self.z_boson_width**4
            * 2
            / (pT * zc**2)
            * 0.3893792922e9
        )

        dsigma_qg_zq = parton_luminosity_qg_zq * matrix_element_qg * common_factor
        dsigma_qqbar_zg = (
            parton_luminosity_qqbar_zg * matrix_element_qqbar * common_factor
        )

        return dsigma_qg_zq, dsigma_qqbar_zg

    def _split_function(self, zc):
        """
        计算分裂函数
        Args:
            zc: 动量分数
            pT: Jet的横动量
            mu: 能量标度

        Returns:
            分裂函数P_qq, P_gq, P_qg, P_gg
            0: delta
            1: regular
            2: plus_0
        """

        CF = self.alphas_runner.CF
        CA = self.alphas_runner.CA
        TF = self.alphas_runner.TR
        NF = 5
        Beta0 = 11.0 / 3.0 * CA - 4.0 / 3.0 * TF * NF

        pqq_delta = CF * 3 / 2
        pqq_regular = 0.0
        pqq_plus_0 = CF * (1 + zc**2)

        pgq_delta = 0.0
        pgq_regular = CF * (1 + (1 - zc) ** 2) / zc
        pgq_plus_0 = 0.0

        pqg_delta = 0.0
        pqg_regular = TF * 2 * (1 - zc) * zc
        pqg_plus_0 = 0.0

        pgg_delta = Beta0 / 2.0
        pgg_regular = 0.0
        pgg_plus_0 = 0.0

        return (
            np.array([pqq_delta, pqq_regular, pqq_plus_0]),
            np.array([pgq_delta, pgq_regular, pgq_plus_0]),
            np.array([pqg_delta, pqg_regular, pqg_plus_0]),
            np.array([pgg_delta, pgg_regular, pgg_plus_0]),
        )

    def _sijet_func(self, zc, pT, mu):
        """
        计算sijet函数
        Args:
            zc: 动量分数
            pT: Jet的横动量
            mu: 能量标度

        Returns:
            sijet_q函数值和sijet_g函数值
            0: delta
            1: regular
            2: plus_0
            3: plus_1
        """

        muJ = self.muF_factor * pT * self.radius

        L = np.log(mu**2 / muJ**2)
        CF = self.alphas_runner.CF

        alg_q = CF * (13 / 2.0 - 2.0 / 3.0 * np.pi**2)

        sijet_func_q_delta = L * self._split_function(zc)[0][0] + alg_q
        sijet_func_q_regular = (
            -1.0 - self._split_function(zc)[1][1] * np.log(1.00 - zc) * 2
        )
        # sijet_func_q_regular = -1.0
        sijet_func_q_plus_0 = L * self._split_function(zc)[0][2]
        sijet_func_q_plus_1 = -CF * 2.0 * (1 + zc**2)

        sijet_func_g_delta = 1.0
        sijet_func_g_regular = 0.0
        sijet_func_g_plus_0 = 0.0
        sijet_func_g_plus_1 = 0.0

        return (
            np.array(
                [
                    sijet_func_q_delta,
                    sijet_func_q_regular,
                    sijet_func_q_plus_0,
                    sijet_func_q_plus_1,
                ]
            ),
            np.array(
                [
                    sijet_func_g_delta,
                    sijet_func_g_regular,
                    sijet_func_g_plus_0,
                    sijet_func_g_plus_1,
                ]
            ),
        )

    def _single_event(
        self,
        y_pT,
        y_eta,
        y_v,
        y_zc,
        pdf_object,
    ):
        """
        计算单个事件的贡献

        Args:
            y_pT: 横动量积分变量 [0,1]
            y_eta: 赝快度积分变量 [0,1]
            y_v: 相空间变量v的积分变量 [0,1]
            y_zc: 动量分数积分变量 [0,1]
            pdf_object: PDF对象

        Returns:
            长度为16的数组，包含各种物理量的计算结果
        """

        #
        #
        #
        #
        #
        # ===== Part1: 通用部分 ======
        #
        #
        #
        #
        #

        # # 通用的零结果
        # zero_result = np.zeros(16)

        # 外层pT，eta积分
        pT = self.pT_range[0] + y_pT * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])
        jacobian = (self.pT_range[1] - self.pT_range[0]) * (
            self.eta_range[1] - self.eta_range[0]
        )

        # 计算重整化和因子化标度
        muR = self.muR_factor * pT
        muF = self.muF_factor * pT
        muJ = self.muF_factor * pT * self.radius

        # 计算不同能标下的强耦合常数(除以2pi)
        alphas_mu_2pi = self.alphas_runner.alphas(muR) / (2 * np.pi)
        alphas_muF_2pi = self.alphas_runner.alphas(muF) / (2 * np.pi)
        alphas_muJ_2pi = self.alphas_runner.alphas(muJ) / (2 * np.pi)

        # 计算相空间变量
        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)

        #
        #
        #
        #
        #
        # ===== Part2 delta(1-zc)的贡献，并提供zc = 1的微分截面 =====
        #
        #
        #
        #
        #

        zc = 1.0
        vmin = V * W / zc
        vmax = 1.0 - (1.0 - V) / zc
        v = vmin + y_v * (vmax - vmin)

        jacobian_zc1 = (vmax - vmin) * jacobian

        dsigma_qg_zq_zc1, dsigma_qqbar_zg_zc1 = (
            np.array(self._LO_dsigma(pT, eta, v, zc, pdf_object)) * jacobian_zc1
        )

        dsigma_qg_zq_qjet_LO, dsigma_qqbar_zg_gjet_LO = (
            np.array(self._LO_dsigma(pT, eta, v, zc, pdf_object)) * jacobian_zc1
        )

        # 由于log(1-z)的存在，避免使用zc=1
        dsigma_qg_zq_qjet_delta = (
            1.0 + alphas_muJ_2pi * self._sijet_func(0.5, pT, muJ)[0][0]
        ) * dsigma_qg_zq_zc1
        dsigma_qqbar_zg_gjet_delta = (
            1.0 + alphas_muJ_2pi * self._sijet_func(0.5, pT, muJ)[1][0]
        ) * dsigma_qqbar_zg_zc1

        #
        #
        #
        #
        #
        # ===== Part3 zc != 1.0 的贡献，将要处理regular,plus_0,plus_1等贡献=====
        #
        #
        #
        #
        #

        # zc从0到1积分
        zc_min = 1.0 - V + V * W
        zc = zc_min + y_zc * (1.0 - zc_min)
        vmin = V * W / zc
        vmax = 1.0 - (1.0 - V) / zc
        v = vmin + y_v * (vmax - vmin)

        jacobian_zc = (vmax - vmin) * jacobian * (1.0 - zc_min)

        dsigma_qg_zq_zc, dsigma_qqbar_zg_zc = (
            np.array(self._LO_dsigma(pT, eta, v, zc, pdf_object)) * jacobian_zc
        )

        # 计算sijet的regular项
        dsigma_qg_zq_qjet_regular = (
            alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[0][1]
        ) * dsigma_qg_zq_zc

        dsigma_qqbar_zg_gjet_regular = (
            alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[1][1]
        ) * dsigma_qqbar_zg_zc

        # 计算sijet的plus_0项
        dsigma_qg_zq_qjet_plus_0 = (
            (alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[0][2]) * dsigma_qg_zq_zc
            - (alphas_muJ_2pi * self._sijet_func(1.0, pT, muJ)[0][2]) * dsigma_qg_zq_zc1
        ) / (1.0 - zc)
        +dsigma_qg_zq_zc1 * np.log(1.0 - zc_min)

        dsigma_qqbar_zg_gjet_plus_0 = (
            (alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[1][2]) * dsigma_qqbar_zg_zc
            - (alphas_muJ_2pi * self._sijet_func(1.0, pT, muJ)[1][2])
            * dsigma_qqbar_zg_zc1
        ) / (1.0 - zc)
        +dsigma_qqbar_zg_zc1 * np.log(1.0 - zc_min)

        # 计算sijet的plus_1项
        dsigma_qg_zq_qjet_plus_1 = (
            (
                (alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[0][3]) * dsigma_qg_zq_zc
                - (alphas_muJ_2pi * self._sijet_func(1.0, pT, muJ)[0][3])
                * dsigma_qg_zq_zc1
            )
            * np.log(1.0 - zc)
            / (1.0 - zc)
        )
        +dsigma_qg_zq_zc1 * (-np.log(1.0 - zc_min) ** 2 / 2.0)

        dsigma_qqbar_zg_gjet_plus_1 = (
            (
                (alphas_muJ_2pi * self._sijet_func(zc, pT, muJ)[1][3])
                * dsigma_qqbar_zg_zc
                - (alphas_muJ_2pi * self._sijet_func(1.0, pT, muJ)[1][3])
                * dsigma_qqbar_zg_zc1
            )
            * np.log(1.0 - zc)
            / (1.0 - zc)
        )
        +dsigma_qqbar_zg_zc1 * (-np.log(1.0 - zc_min) ** 2 / 2.0)

        dsigma_qg_zq_qjet_NLO = (
            dsigma_qg_zq_qjet_delta
            + dsigma_qg_zq_qjet_regular
            + dsigma_qg_zq_qjet_plus_0
            + dsigma_qg_zq_qjet_plus_1
        )
        dsigma_qqbar_zg_gjet_NLO = (
            dsigma_qqbar_zg_gjet_delta
            + dsigma_qqbar_zg_gjet_regular
            + dsigma_qqbar_zg_gjet_plus_0
            + dsigma_qqbar_zg_gjet_plus_1
        )
        return np.array(
            [
                np.log(1 - zc) / zc / zc,
                dsigma_qg_zq_qjet_NLO,
                dsigma_qqbar_zg_gjet_NLO,
                dsigma_qg_zq_qjet_LO,
                dsigma_qqbar_zg_gjet_LO,
                6,
                7,
                8,
                9,
                10,
                11,
                12,
                13,
                14,
                15,
                16,
            ]
        )


class EECFitCalculator:
    def __init__(self, pdf_name: str, ALS_MZ: float = 0.118):

        print("正在初始化截面计算器...")
        try:
            self.pdf_name = pdf_name
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)

        except Exception as e:
            print(f"错误: 无法加载LHAPDF集 '{pdf_name}'。请确保已正确安装并配置。")
            raise e
        self.ALS_MZ = ALS_MZ
        self.alpha_s_runner = AlphaSRunner(ALS_MZ=self.ALS_MZ)
        print("初始化完成。")

    def calculate(
        self,
        radius: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        scale_factors: Tuple[float, float] = (1.0, 1.0),
        vegas_params: dict = None,
    ) -> vegas.Integrator:

        if vegas_params is None:
            vegas_params = {"nitn": 10, "neval": 50000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        integrand = ZJetEECVegasIntegrand(
            pdf_name=self.pdf_name,
            alphas_runner=self.alpha_s_runner,
            Ecom=14000,
            pT_range=pT_range,
            eta_range=eta_range,
            radius=radius,
            muR_factor=scale_factors[0],
            muF_factor=scale_factors[1],
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1], [0, 1]]

        integ = vegas.Integrator(integration_bounds)

        result = integ(integrand, **vegas_params)

        print("\n--- 计算结束 ---")
        print(result.summary())

        return result


if __name__ == "__main__":
    # alpha_s_runner = AlphaSRunner(ALS_MZ =0.118)
    # print(alpha_s_runner.alpha_s(40))
    multiprocessing.set_start_method("spawn", force=True)

    fit = np.array([0.0418351, 1.57334925])
    # fit = np.array([0.03188174,1.27056611])
    # fit_NLO = np.array([0.1712028,1.60790194])
    try:
        calculator = EECFitCalculator(
            pdf_name="NNPDF31_nlo_as_0118", ALS_MZ=0.118 * (1.000)
        )

        pT_min, pT_max = 480, 540
        eta_min, eta_max = -1.0, 1.0
        jet_radius = 0.6

        # VEGAS parameters (adjust for desired precision/speed)
        vegas_config = {
            "nitn": 10,  # Number of iterations
            "neval": 50000,  # Number of evaluations per iteration
            "nproc": 16,  # Number of parallel processes
        }

        # 3. Run the calculation
        final_result = calculator.calculate(
            radius=jet_radius,
            pT_range=(pT_min, pT_max),
            eta_range=(eta_min, eta_max),
            scale_factors=(1.0, 1.0),  # Central scale
            vegas_params=vegas_config,
        )

        result = final_result.flatten()

        print(result)

        print(
            (fit[0] * (result[0] + result[2]) + fit[1] * (result[1] + result[3]))
            / (result[12] + result[13])
        )

    except Exception as e:
        print(f"\n程序执行时发生错误: {e}")
        # 打印更详细的追溯信息以帮助调试
        import traceback

        traceback.print_exc()

        # # sijet函数从0到1的积分
        # momentum_fraction_zc = y_momentum_fraction
        # # QCD常数和分裂函数定义
        # quark_anomalous_dimension = AlphaSRunner.CF * (
        #     13 / 2 - 2 / 3 * AlphaSRunner.PI**2
        # )

        # # 分裂函数 P_qq, P_gq
        # splitting_function_qq = lambda z: AlphaSRunner.CF * np.array(
        #     [3 / 2, 0, (1 + z**2)]
        # )
        # splitting_function_gq = lambda z: AlphaSRunner.CF * np.array(
        #     [0, (1 + (1 - z) ** 2) / z, 0]
        # )

        # # 夸克通道的前因子
        # quark_prefactor = lambda z: 2.0 * AlphaSRunner.CF * (1 + z**2)

        # # 对数因子
        # log_scale_factor = np.log(
        #     self.factorization_scale_factor * self.factorization_scale_factor
        # )

        # # 夸克通道的sijet函数
        # sijet_quark_function = lambda z: np.array(
        #     [
        #         quark_anomalous_dimension
        #         + splitting_function_qq(z)[0] * log_scale_factor,
        #         (-1.0 + splitting_function_gq(z)[1] * log_scale_factor),
        #         (splitting_function_qq(z)[2] - splitting_function_qq(1)[2])
        #         / (1 - z)
        #         * log_scale_factor,
        #         -(quark_prefactor(z) - quark_prefactor(1)) * np.log(1 - z) / (1 - z),
        #         -2 * splitting_function_gq(z)[1] * np.log(1 - z),
        #     ]
        # )

        # # 胶子通道相关常数
        # NUM_FLAVORS = 5.0
        # FLAVOR_FACTOR = 0.5
        # gluon_anomalous_dimension = (
        #     AlphaSRunner.CA * (67 / 9 - 2 * AlphaSRunner.PI**2 / 3)
        #     - FLAVOR_FACTOR * NUM_FLAVORS * 23 / 9
        # )

        # # β函数系数
        # beta_function_coeff = (
        #     11 * AlphaSRunner.CA - 2 * FLAVOR_FACTOR * NUM_FLAVORS
        # ) / 3.0

        # # 分裂函数 P_gg, P_qg
        # splitting_function_gg = (
        #     lambda z: 2
        #     * AlphaSRunner.CA
        #     * np.array([beta_function_coeff / 2, (1 - z) / z + z * (1 - z), z])
        # )
        # splitting_function_qg = lambda z: AlphaSRunner.CA * np.array(
        #     [0, FLAVOR_FACTOR * (z**2 + (1 - z) ** 2), 0]
        # )

        # # 胶子通道的前因子
        # gluon_prefactor = lambda z: 4.0 * AlphaSRunner.CA * (1 - z + z**2) ** 2 / z

        # # 用于输出的对数项
        # log_term_for_output = np.log(1 - momentum_fraction_zc)

        # # 胶子通道的sijet函数
        # sijet_gluon_function = lambda z: np.array(
        #     [
        #         gluon_anomalous_dimension
        #         + splitting_function_gg(z)[0] * log_scale_factor,
        #         2
        #         * NUM_FLAVORS
        #         * (
        #             -2.0 * FLAVOR_FACTOR * z * (1 - z)
        #             + splitting_function_qg(z)[1] * log_scale_factor
        #         ),
        #         (splitting_function_gg(z)[2] - splitting_function_gg(1)[2])
        #         / (1 - z)
        #         * log_scale_factor,
        #         -(gluon_prefactor(z) - gluon_prefactor(1)) * np.log(1 - z) / (1 - z),
        #         -4 * NUM_FLAVORS * splitting_function_qg(z)[1] * np.log(1 - z),
        #     ]
        # )

        # # zc = 1 的贡献，从0到1-error的积分，所有项都是regular
        # # zc = y_z*(1-error)

        # # sijet_q_1_2 = lambda zc: np.array([
        # #                                 0,
        # #                                 (-1.0+Pgq(zc)[1]*L)*zc**(N-2),
        # #                                 zc**(N-2)*Pqq(zc)[2]/(1-zc)*L,
        # #                                 -zc**(N-2)*prefactor_q(zc)*np.log(1-zc)/(1-zc),
        # #                                 -2*Pgq(zc)[1]*np.log(1-zc)*zc**(N-2)
        # #                                 ])
        # # sijet_g_1_2 = lambda zc: np.array([
        # #                                 0,
        # #                                 2*nf*(-2.0*Tf*zc*(1-zc)+Pqg(zc)[1]*L)*zc**(N-2),
        # #                                 zc**(N-2)*Pgg(zc)[2]/(1-zc)*L,
        # #                                 -zc**(N-2)*prefactor_g(zc)*np.log(1-zc)/(1-zc),
        # #                                 -4*nf*Pqg(zc)[1]*np.log(1-zc)*zc**(N-2)
        # #                                 ])
        # # 计算NLO修正
        # nlo_correction_quark = (
        #     differential_cross_section_qg_zc1
        #     * alpha_s_value
        #     / (2 * np.pi)
        #     * sum([sijet_quark_function(momentum_fraction_zc)[i] for i in range(5)])
        # )
        # nlo_correction_gluon = (
        #     differential_cross_section_qqbar_zc1
        #     * alpha_s_value
        #     / (2 * np.pi)
        #     * sum([sijet_gluon_function(momentum_fraction_zc)[i] for i in range(5)])
        # )

        # # # 从zcmin到1-error的积分，所有项都是regular
        # # zcmin = 1.0 - V + V*W
        # # zc = zcmin + y_z * (1.0-error - zcmin)

        # # v_min = V * W / zc
        # # v_max = 1.0 - (1.0 - V) / zc
        # # if v_min >= 1 or v_max >= 1:
        # #     return np.zeros(16)
        # # v = v_min + y_v * (v_max - v_min)
        # # jacobian=(self.pT_range[1] - self.pT_range[0]) * (
        # #     self.eta_range[1] - self.eta_range[0]
        # # )*(1-error-zcmin)*(v_max-v_min)

        # # w = 1.0
        # # x1 = self.phase_space.x1(v, w, zc, pT, eta)
        # # x2 = self.phase_space.x2(v, w, zc, pT, eta)
        # # if not (0 < x1 < 1 and 0 < x2 < 1):
        # #     return np.zeros(16)
        # # shat = self.phase_space.shat(v, w, zc, pT, eta)
        # # if shat <= 0:
        # #     return np.zeros(16)
        # # uhat = self.phase_space.u(pT, eta) * x2 / zc
        # # that = self.phase_space.t(pT, eta) * x1 / zc
        # # if uhat == 0 or that == 0:
        # #     return np.zeros(16)

        # # sijet_q_1_3 = lambda zc: np.array([
        # #                                 0,
        # #                                 (-1.0+Pgq(zc)[1]*L)*zc,
        # #                                 Pqq(zc)[2]/(1-zc)*L,
        # #                                 -prefactor_q(zc)*np.log(1-zc)/(1-zc),
        # #                                 -2*Pgq(zc)[1]*np.log(1-zc)
        # #                                 ])
        # # sijet_g_1_3 = lambda zc: np.array([
        # #                                 0,
        # #                                 4*nf*(-Tf*zc*(1-zc)+Pqg(zc)[1]*L),
        # #                                 Pgg(zc)[2]/(1-zc)*L,
        # #                                 -prefactor_g(zc)*np.log(1-zc)/(1-zc),
        # #                                 -4*nf*Pqg(zc)[1]*np.log(1-zc)
        # #                                 ])
        # # # === 4. 部分子光度与矩阵元计算 ===
        # # # 初始化夸克-胶子(qg)和夸克-反夸克(qqbar)通道的部分子光度。
        # # lumi_qg, lumi_qqbar = 0.0, 0.0

        # # # 循环遍历所有参与反应的夸克味。
        # # for qf in self.QUARK_FLAVORS_TO_SUM:
        # #     # 获取夸克和胶子的PDF ID。
        # #     q, g = qf, 21
        # #     # 获取夸克与Z玻色子的电弱耦合系数。
        # #     z_coupling = self.phase_space.vq_plus_aq(qf)

        # #     # 从PDF对象中获取在标度mu_F和动量分数x1, x2处的PDF值。
        # #     pdf_q_x1 = pdf.xfxQ(q, x1, mu_F)
        # #     pdf_qbar_x1 = pdf.xfxQ(-q, x1, mu_F)
        # #     pdf_g_x1 = pdf.xfxQ(g, x1, mu_F)

        # #     pdf_q_x2 = pdf.xfxQ(q, x2, mu_F)
        # #     pdf_qbar_x2 = pdf.xfxQ(-q, x2, mu_F)
        # #     pdf_g_x2 = pdf.xfxQ(g, x2, mu_F)

        # #     # 计算并累加 qg -> Zq 和 gq -> Zq 过程的光度。
        # #     lumi_qg += (
        # #         (pdf_q_x1 + pdf_qbar_x1) * pdf_g_x2
        # #         + pdf_g_x1 * (pdf_q_x2 + pdf_qbar_x2)
        # #     ) * z_coupling

        # #     # 计算并累加 qqbar -> Zg 过程的光度。
        # #     lumi_qqbar += (pdf_q_x1 * pdf_qbar_x2 + pdf_qbar_x1 * pdf_q_x2) * z_coupling

        # # # 计算曼德尔斯坦变量 uhat 和 that。
        # # uhat = self.phase_space.u(pT, eta) * x2 / zc
        # # that = self.phase_space.t(pT, eta) * x1 / zc

        # # if uhat == 0 or that == 0:
        # #     return np.zeros(16)
        # # # 计算领头阶(LO)的硬散射矩阵元。
        # # me_qg_num = shat**2 + uhat**2 - 2.0 * self.MZ**2 * that
        # # matrix_element_qg = -1.0 / 12.0 * (me_qg_num / (shat * uhat))

        # # me_qqbar_num = that**2 + uhat**2 + 2.0 * self.MZ**2 * shat
        # # matrix_element_qqbar = 2.0 / 9.0 * (me_qqbar_num / (that * uhat))

        # # common_factor = (
        # #     (alpha_s / (8.0 * shat)) * self.GZ**4 * 2 / (pT * zc**2) * 0.3893792922e9
        # # )

        # # dsig_qg = lumi_qg * matrix_element_qg * common_factor * jacobian
        # # dsig_qqbar = lumi_qqbar * matrix_element_qqbar * common_factor * jacobian

        # # dsig_q_1 += dsig_qg *alpha_s/2/np.pi* sum([sijet_q_1_3(zc)[i] for i in range(5)])
        # # dsig_g_1 += dsig_qqbar *alpha_s/2/np.pi* sum([sijet_g_1_3(zc)[i] for i in range(5)])

        # # print(dsig_q_1, dsig_g_1)

        # # dsig_qg_NLO = dsig_qg * (
        # #     1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CF*(8131/450-13/2 + 91/30 * np.log(self.kappa_F* self.kappa_F))
        # # )
        # # dsig_qqbar_NLO = dsig_qqbar * (
        # #     1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CA* (411917/22050 - 67/9 -(33716/22050/2*5 -23/9/2*5) + (-181/70* AlphaSRunner.CA-5/3) * np.log(self.kappa_F * self.kappa_F))
        # # )
        # # 设置NLO微分截面
        # differential_cross_section_qg_nlo = nlo_correction_quark
        # differential_cross_section_qqbar_nlo = nlo_correction_gluon

        # # 构建LO微分截面向量
        # differential_cross_section_vector_lo = np.array(
        #     [differential_cross_section_qg_zc1, differential_cross_section_qqbar_zc1]
        # )

        # # 构建NLO微分截面向量
        # differential_cross_section_vector_nlo = np.array(
        #     [differential_cross_section_qg_nlo, differential_cross_section_qqbar_nlo]
        # )

        # # 初始标度
        # INITIAL_SCALE = 50

        # # 计算不同阶的演化算符
        # evolution_operator_ll = self.eec_evolver_leading_log.get_evolution_operator(
        #     INITIAL_SCALE, transverse_momentum, self.jet_radius, m=0
        # )
        # evolution_operator_nll = (
        #     self.eec_evolver_next_to_leading_log.get_evolution_operator(
        #         INITIAL_SCALE, transverse_momentum, self.jet_radius, m=4
        #     )
        # )
        # evolution_operator_nnll = (
        #     self.eec_evolver_next_to_next_leading_log.get_evolution_operator(
        #         INITIAL_SCALE, transverse_momentum, self.jet_radius, m=4
        #     )
        # )

        # # 获取初始算符
        # initial_operator = self.eec_evolver_leading_log.get_operator_fit(
        #     INITIAL_SCALE, self.jet_radius
        # )

        # # 计算演化后的算符
        # evolved_operator_ll = evolution_operator_ll @ initial_operator
        # evolved_operator_nll = evolution_operator_nll @ initial_operator
        # evolved_operator_nnll = evolution_operator_nnll @ initial_operator

        # # LL阶贡献
        # ll_qg_00 = differential_cross_section_vector_lo[0] * evolved_operator_ll[0][0]
        # ll_qg_01 = differential_cross_section_vector_lo[0] * evolved_operator_ll[0][1]
        # ll_qqbar_10 = (
        #     differential_cross_section_vector_lo[1] * evolved_operator_ll[1][0]
        # )
        # ll_qqbar_11 = (
        #     differential_cross_section_vector_lo[1] * evolved_operator_ll[1][1]
        # )

        # # NLL阶贡献
        # nll_qg_00 = (
        #     differential_cross_section_vector_nlo[0] * evolved_operator_nll[0][0]
        # )
        # nll_qqbar_10 = (
        #     differential_cross_section_vector_nlo[1] * evolved_operator_nll[1][0]
        # )
        # nll_qg_01 = (
        #     differential_cross_section_vector_nlo[0] * evolved_operator_nll[0][1]
        # )
        # nll_qqbar_11 = (
        #     differential_cross_section_vector_nlo[1] * evolved_operator_nll[1][1]
        # )

        # # NNLL阶贡献
        # nnll_qg_00 = (
        #     differential_cross_section_vector_nlo[0] * evolved_operator_nnll[0][0]
        # )
        # nnll_qg_01 = (
        #     differential_cross_section_vector_nlo[0] * evolved_operator_nnll[0][1]
        # )
        # nnll_qqbar_10 = (
        #     differential_cross_section_vector_nlo[1] * evolved_operator_nnll[1][0]
        # )
        # nnll_qqbar_11 = (
        #     differential_cross_section_vector_nlo[1] * evolved_operator_nnll[1][1]
        # )
        # # print((fit[0]*(result[0]+result[1]))/(result[12]))
        # # print((fit[1]*(result[3]))/(result[13]))
        # # print(result_res)

        # fit = solve_linear_system(
        #     result[0].mean,
        #     result[1].mean,
        #     0.0154 * result[12].mean,
        #     result[2].mean,
        #     result[3].mean,
        #     0.0047 * result[13].mean,
        # )

        # return np.array(
        #     [
        #         log_term_for_output,
        #         differential_cross_section_qg_nlo / differential_cross_section_qg_zc1,
        #         differential_cross_section_qqbar_nlo
        #         / differential_cross_section_qqbar_zc1,
        #         differential_cross_section_qqbar_zc1,
        #         ll_qg_00,
        #         ll_qg_01,
        #         ll_qqbar_10,
        #         ll_qqbar_11,
        #         nll_qg_00,
        #         nll_qqbar_10,
        #         nll_qg_01,
        #         nll_qqbar_11,
        #         nnll_qg_00,
        #         nnll_qg_01,
        #         nnll_qqbar_10,
        #         nnll_qqbar_11,
        #     ]
        # )
