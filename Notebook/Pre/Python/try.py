import numpy as np

def f(x):
    """要积分的函数"""
    return np.log(1 - x)

def monte_carlo_integrate(a, b, n_samples=1000000):
    """
    使用蒙特卡洛方法计算函数 f(x) 在 [a, b] 区间上的定积分。
    """
    # 1. 在 [a, b] 区间内生成均匀分布的随机点
    random_points = np.random.uniform(a, b, n_samples)
    
    # 2. 计算每个随机点上的函数值 (向量化操作)
    function_values = f(random_points)
    
    # 3. 计算函数值的平均值
    average_value = np.mean(function_values)
    
    # 4. 估算积分值：(区间宽度) * (函数平均值)
    integral_estimate = (b - a) * average_value
    
    return integral_estimate

if __name__ == "__main__":
    # 定义积分区间和采样点数
    integration_min = 0.0
    integration_max = 1.0  # 避开奇点 x=1
    num_samples = 500000000   # 增加采样点数以提高精度

    # 设置随机种子以保证结果可复现
    np.random.seed(42)

    # 执行积分
    result = monte_carlo_integrate(integration_min, integration_max, num_samples)

    print(f"Python (NumPy) Implementation")
    print(f"Integration interval: [{integration_min}, {integration_max}]")
    print(f"Number of samples: {num_samples}")
    print(f"Monte Carlo estimate: {result:.7f}")
    print(f"Analytical solution:  -0.8002135")