# EEC截面计算器变量命名改进总结

## 主要改进内容

### 1. 函数名改进
- `solve_equations` → `solve_linear_system`
  - 更准确地描述了函数功能（求解线性方程组）
  - 添加了详细的类型注解和文档字符串
  - 改进了数值稳定性检查（使用容差而不是严格等于0）

### 2. 类名改进
- `ZJet_EEC_VegasIntegrand` → `ZJetEECVegasIntegrand`
  - 遵循Python类命名规范（PascalCase，不使用下划线）

### 3. 参数和变量名改进

#### 构造函数参数
- `alpha_s` → `alpha_s_runner`
- `pT_range` → `transverse_momentum_range`
- `eta_range` → `pseudorapidity_range`
- `R` → `jet_radius`
- `kappa_R` → `renormalization_scale_factor`
- `kappa_F` → `factorization_scale_factor`

#### 实例变量
- `self.MZ` → `self.z_boson_mass`
- `self.GZ` → `self.z_boson_width`
- `self.eec_evolver_LL` → `self.eec_evolver_leading_log`
- `self.eec_evolver_NLL` → `self.eec_evolver_next_to_leading_log`
- `self.eec_evolver_NNLL` → `self.eec_evolver_next_to_next_leading_log`

#### 物理量变量
- `pT` → `transverse_momentum`
- `eta` → `pseudorapidity`
- `V`, `W` → `phase_space_v_var`, `phase_space_w_var`
- `x1`, `x2` → `momentum_fraction_x1`, `momentum_fraction_x2`
- `shat` → `invariant_mass_squared`
- `uhat`, `that` → `mandelstam_u`, `mandelstam_t`
- `mu_R`, `mu_F` → `renormalization_scale`, `factorization_scale`
- `alpha_s` → `alpha_s_value`

#### 部分子光度和PDF相关
- `lumi_qg`, `lumi_qqbar` → `parton_luminosity_qg`, `parton_luminosity_qqbar`
- `pdf` → `pdf_object`
- `qf` → `quark_flavor`
- `q`, `g` → `quark_pdg_id`, `gluon_pdg_id`
- `z_coupling` → `z_boson_coupling`
- `pdf_q_x1` → `pdf_quark_x1` (等等)

#### 矩阵元和截面
- `me_qg_num` → `matrix_element_qg_numerator`
- `matrix_element_qg`, `matrix_element_qqbar` → 保持不变（已经很清晰）
- `common_factor` → `common_prefactor`
- `dsig_qg_zc_1` → `differential_cross_section_qg_zc1`
- `dsig_qqbar_zc_1` → `differential_cross_section_qqbar_zc1`

#### QCD常数和分裂函数
- `nf`, `Tf` → `NUM_FLAVORS`, `FLAVOR_FACTOR`
- `q_alg`, `g_alg` → `quark_anomalous_dimension`, `gluon_anomalous_dimension`
- `Pqq`, `Pgq`, `Pgg`, `Pqg` → `splitting_function_qq`, `splitting_function_gq`, 等等
- `L` → `log_scale_factor`
- `sijet_q_1_1`, `sijet_g_1_1` → `sijet_quark_function`, `sijet_gluon_function`

#### 演化算符相关
- `Q0` → `INITIAL_SCALE`
- `U_LL`, `U_NLL`, `U_NNLL` → `evolution_operator_ll`, `evolution_operator_nll`, `evolution_operator_nnll`
- `A0_pow` → `initial_operator`
- `A_LL_pow` → `evolved_operator_ll` (等等)

#### 单字母变量的改进
原来的单字母变量 `a, b, c, d, e, f, g, h, i, j, k, l` 被替换为：
- `ll_qg_00`, `ll_qg_01`, `ll_qqbar_10`, `ll_qqbar_11` (LL阶贡献)
- `nll_qg_00`, `nll_qqbar_10`, `nll_qg_01`, `nll_qqbar_11` (NLL阶贡献)
- `nnll_qg_00`, `nnll_qg_01`, `nnll_qqbar_10`, `nnll_qqbar_11` (NNLL阶贡献)

### 4. 常量命名改进
- 使用大写命名常量：`NUM_FLAVORS = 5.0`, `FLAVOR_FACTOR = 0.5`, `INITIAL_SCALE = 50`

### 5. 函数参数命名改进
- `y_pt`, `y_eta`, `y_v`, `y_z` → `y_transverse_momentum`, `y_pseudorapidity`, `y_phase_space_v`, `y_momentum_fraction`

### 6. 添加的文档字符串
- 为主要函数和类添加了详细的文档字符串
- 说明了参数的物理含义和返回值的结构

## 改进效果

1. **可读性大幅提升**：变量名现在清楚地表达了其物理含义
2. **维护性增强**：其他开发者（包括未来的自己）更容易理解代码
3. **减少错误**：描述性的变量名减少了混淆和错误的可能性
4. **符合Python规范**：遵循了PEP 8命名规范
5. **物理意义明确**：每个变量都清楚地表达了其在物理计算中的作用

## 建议的后续改进

1. **函数拆分**：将长函数（如`_single_event`）拆分为更小的、功能单一的函数
2. **类型注解**：为所有函数添加完整的类型注解
3. **错误处理**：添加更多的输入验证和错误处理
4. **配置文件**：将硬编码的常数移到配置文件中
5. **单元测试**：为重构后的代码编写单元测试
