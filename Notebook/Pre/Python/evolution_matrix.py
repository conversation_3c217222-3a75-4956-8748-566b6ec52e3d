import numpy as np
from scipy.linalg import solve_sylvester, expm
from typing import <PERSON><PERSON>, Dict, List

# from ALZ_runner import Alpha<PERSON>unner
from ALZ_running import AlphaSRunner


class EvolutionMatrix:

    def __init__(
        self, order: int = 2, kappa: float = 1.0, ALZ_runner: AlphaSRunner = None
    ):
        if order < 0:
            raise ValueError("Order must be a non-negative integer.")
        self._cache: Dict[Tuple[int, int], Tuple[Dict, Dict]] = {}
        self.order = order
        self.kappa = kappa

        if ALZ_runner is None:
            self.ALZ_runner = AlphaSRunner(ALS_MZ=0.118)
        else:
            self.ALZ_runner = ALZ_runner

        self.CF = self.ALZ_runner.CF
        self.CA = self.ALZ_runner.CA
        self.ZETA2 = self.ALZ_runner.ZETA2
        self.ZETA3 = self.ALZ_runner.ZETA3
        self.ZETA4 = self.ALZ_runner.ZETA4
        self.ZETA5 = self.ALZ_runner.ZETA5
        self.PI = self.ALZ_runner.PI

    def evolve(
        self, pdf0: np.ndarray, Q0: float, Q: float, R: float, m: int = 2
    ) -> np.ndarray:
        if Q0 == Q:
            return pdf0

        evolution_operator = self.get_evolution_operator(Q0, Q, R, m)
        return evolution_operator @ pdf0

    def get_evolution_operator(
        self, Q0: float, Q: float, R: float = 1.0, m: int = 2
    ) -> np.ndarray:
        if Q0 == Q:
            return np.eye(2)

        path, nfs = self._get_scale_nodes(Q0, Q, self.kappa, R)
        total_operator = np.eye(2)

        for i in range(len(path) - 1):
            q1, q2 = path[i], path[i + 1]
            nf = nfs[i]
            segment_operator = self._get_single_segment_operator(q1, q2, nf, m)
            total_operator = segment_operator @ total_operator

        return total_operator

    def get_operator_fit(self, Q: float, R: float = 1.0) -> np.ndarray:

        nfs = self.ALZ_runner._nf_at(Q * self.kappa)
        _gamma_0 = self.gamma0(nfs)
        _beta_0 = self.ALZ_runner._beta0(nfs)*4*self.PI
        alpha_s_val = self.ALZ_runner.alpha_s(Q * R, self.kappa)
        return expm(np.log(alpha_s_val) * _gamma_0 / _beta_0)

    # ---------- 4. 内部核心计算方法 ----------
    def _get_single_segment_operator(
        self, Q0: float, Q: float, nf: int, m: int
    ) -> np.ndarray:
        cache_key = (nf, m)
        if cache_key not in self._cache:
            self._cache[cache_key] = self._compute_and_cache_matrices(nf, m)
        r_dict, u_dict = self._cache[cache_key]

        a0 = self.ALZ_runner.alpha_s(Q0) / (4 * self.PI)
        a = self.ALZ_runner.alpha_s(Q) / (4 * self.PI)

        if a <= 0 or a0 <= 0 or a / a0 <= 0:
            raise ValueError(
                f"非物理的 alpha_s 值导致演化失败: a({Q:.2f})={a:.4e}, a0({Q0:.2f})={a0:.4e}"
            )

        L = expm(-np.log(a / a0) * r_dict[0])
        S0 = self._calculate_s_matrix(Q0, m, u_dict)
        S = self._calculate_s_matrix(Q, m, u_dict)
        return S @ L @ np.linalg.inv(S0)

    def _calculate_s_matrix(
        self, Q: float, m: int, u_dict: Dict[int, np.ndarray]
    ) -> np.ndarray:

        a = self.ALZ_runner.alpha_s(Q) / (4 * self.PI)
        S = np.eye(2)
        for k in range(1, m + 1):
            if k in u_dict:
                S += (a**k) * u_dict[k]
        return S

    def _compute_and_cache_matrices(self, nf: int, m: int) -> Tuple[dict, dict]:
        """计算并缓存 R 和 U 矩阵。"""
        r_dict = self._calculate_r_matrices(nf, n_max=m)
        u_dict = self._solve_u_matrices(r_dict, n_max=m)
        return r_dict, u_dict

    def _calculate_r_matrices(self, nf: int, n_max: int) -> Dict[int, np.ndarray]:
        b_known = [
            self.ALZ_runner._beta0(nf)*(4*self.PI),
            self.ALZ_runner._beta1(nf)*((4**2)*self.PI**2),
            self.ALZ_runner._beta2(nf)*((4**3)*self.PI**3),
        ]
        g_known = [self.gamma0(nf), self.gamma1(nf), self.gamma2(nf)]
        r = {}

        series_truncation_order = self.order

        for n in range(n_max + 1):
            g_n = (
                g_known[n]
                if n <= series_truncation_order and n < len(g_known)
                else np.zeros((2, 2))
            )
            sum_term = np.zeros((2, 2))
            for k in range(1, n + 1):
                if k <= series_truncation_order and k < len(b_known):
                    b_k = b_known[k]
                    sum_term += b_k * r[n - k]

            if b_known[0] == 0:
                raise ValueError("beta_0 is zero, cannot perform division.")

            r[n] = (-g_n - sum_term) / b_known[0]
        return r

    def _solve_u_matrices(
        self, r_dict: Dict[int, np.ndarray], n_max: int
    ) -> Dict[int, np.ndarray]:
        u_dict = {}
        R0 = r_dict[0]
        I = np.eye(2)
        for n in range(1, n_max + 1):
            Rn = r_dict.get(n, np.zeros((2, 2)))
            A = -R0 - n * I
            B = R0
            C = Rn + sum(r_dict[n - k] @ u_dict[k] for k in range(1, n))
            u_dict[n] = solve_sylvester(A, B, C)
        return u_dict

    def _get_scale_nodes(
        self, Q0: float, Q: float, kappa: float, R: float
    ) -> Tuple[List[float], List[int]]:
        q_start, q_end = kappa * Q0 * R, kappa * Q * R

        nodes = {q_start, q_end}
        for m_thresh, _ in self.ALZ_runner.THRESH:
            if min(q_start, q_end) <= m_thresh <= max(q_start, q_end):
                nodes.add(m_thresh)
        path = sorted(list(nodes), reverse=(q_end < q_start))
        nfs = [
            self.ALZ_runner._nf_at((path[i] + path[i + 1]) / 2.0)
            for i in range(len(path) - 1)
        ]
        # print(nodes)
        return path, nfs

    @staticmethod
    def gamma0(nf: int) -> np.ndarray:
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        return np.array(
            [[91 * CF / 15, -32 * nf / 105], [-8 * CF / 15, 181 * CA / 35 + 2 * nf / 3]]
        )

    @staticmethod
    def gamma1(nf: int) -> np.ndarray:
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        Z2, Z3 = AlphaSRunner.ZETA2, AlphaSRunner.ZETA3
        qq = (
            -604601 * CF * nf / 110250
            + CF * CF * (-474221 / 13500 + 24 * Z2 - 16 * Z3)
            + CA * CF * (520837 / 6750 - 544 * Z2 / 15 + 8 * Z3)
        )
        qg = (
            (-19792 * CF * nf) / 7875
            - (1024 * nf**2) / 3675
            + CA * nf * (1999 / 18375 + (128 * Z2) / 105)
        )
        gq = CA * CF * (-2882863 / 661500 - (32 * Z2) / 15) + CF**2 * (
            -9374 / 3375 + (64 * Z2) / 15
        )
        gg = (
            (340066 * CF * nf) / 165375
            + CA * nf * (26399 / 33075 - (16 * Z2) / 3)
            + CA**2 * (4706626 / 165375 - (632 * Z2) / 105 - 8 * Z3)
        )
        return np.array([[qq, qg], [gq, gg]])

    @staticmethod
    def gamma2(nf: int) -> np.ndarray:
        """返回2阶 (NNLO) 反常维度矩阵 gamma_2。"""
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        Zeta2, Zeta3, Zeta4, Zeta5 = (
            AlphaSRunner.ZETA2,
            AlphaSRunner.ZETA3,
            AlphaSRunner.ZETA4,
            AlphaSRunner.ZETA5,
        )
        qq = (-19521281 / 13891500) * CF * nf**2
        +(
            -3829448611 / 33075000
            - (5647384 * Zeta2) / 165375
            + (337208 * Zeta3) / 1575
            - (136 * Zeta4) / 3
        ) * CF**2 * nf
        +(
            -59504161907 / 4167450000
            + (2198426 * Zeta2) / 33075
            - (257336 * Zeta3) / 1575
            + (68 * Zeta4) / 3
        ) * CA * CF * nf
        +(
            3623379121 / 1518750
            - (1101667 * Zeta2) / 1125
            - (235402 * Zeta3) / 75
            - 208 * Zeta2 * Zeta3
            + (51064 * Zeta4) / 15
            - 432 * Zeta5
        ) * CA * CF**2
        +(
            -4180165661 / 16200000
            - (730997 * Zeta2) / 3375
            + (281558 * Zeta3) / 225
            + 48 * Zeta2 * Zeta3
            - (12752 * Zeta4) / 15
            + 112 * Zeta5
        ) * CA**2 * CF
        +(
            -161877491 / 81000
            + (973994 * Zeta2) / 1125
            + (29756 * Zeta3) / 15
            + 224 * Zeta2 * Zeta3
            - (7312 * Zeta4) / 3
            + 416 * Zeta5
        ) * CF**3

        qg = (-34936 / 128625) * nf**3
        +(-304184494 / 86821875 + (163456 * Zeta2) / 33075) * CF * nf**2
        +(32725682 / 5788125 - (7304 * Zeta2) / 2205 - (128 * Zeta3) / 63) * CA * nf**2
        +(
            -89580154991 / 1041862500
            + (1961024 * Zeta2) / 165375
            + (104064 * Zeta3) / 1225
            - (192 * Zeta4) / 35
        ) * CA * CF * nf
        +(
            1901298533 / 119070000
            + (18545284 * Zeta2) / 1157625
            - (7552 * Zeta3) / 735
            - (1152 * Zeta4) / 35
        ) * CA**2 * nf
        +(
            2857982668 / 37209375
            - (154544 * Zeta2) / 33075
            - (1073536 * Zeta3) / 11025
            + (2048 * Zeta4) / 105
        ) * CF**2 * nf

        gq = (
            (-79552063 / 25725000 + (196 * Zeta2) / 225 - (64 * Zeta3) / 45)
            * CF**2
            * nf
        )
        +(
            -1414558213 / 12150000
            + (91562 * Zeta2) / 3375
            + (3744 * Zeta3) / 25
            - (704 * Zeta4) / 5
        ) * CF**3
        +(
            -778603499 / 22680000
            - (8690054 * Zeta2) / 165375
            - (3632 * Zeta3) / 1575
            + (448 * Zeta4) / 15
        ) * CA**2 * CF
        +(359997809 / 92610000 - (12 * Zeta2) / 5 + (416 * Zeta3) / 45) * CA * CF * nf
        +(
            4485362407 / 34020000
            + (13794439 * Zeta2) / 165375
            - (4160 * Zeta3) / 21
            + (1168 * Zeta4) / 15
        ) * CA * CF**2

        gg = (-(43545391 / 52093125) - (2048 * Zeta2) / 4725) * CF * nf**2
        +(
            -(110295583 / 74418750) + (112936 * Zeta2) / 55125 - (256 * Zeta3) / 225
        ) * CF**2 * nf
        +(-(128595883 / 41674500) + (160 * Zeta2) / 27 - (64 * Zeta3) / 9) * CA * nf**2
        +(
            -(13864219709 / 347287500)
            - (3258284 * Zeta2) / 165375
            + (12672 * Zeta3) / 175
        ) * CA * CF * nf
        +(
            -(88337469301 / 4167450000)
            - (4654366 * Zeta2) / 55125
            + (2336 * Zeta3) / 1575
            + 104 * Zeta4
        ) * CA**2 * nf
        +(
            11996763263 / 463050000
            - (185369599 * Zeta2) / 1157625
            + (698828 * Zeta3) / 11025
            + 64 * Zeta2 * Zeta3
            - (2524 * Zeta4) / 105
            + 96 * Zeta5
        ) * CA**3
        return np.array([[qq, qg], [gq, gg]])


# -------------------- 使用示例 --------------------
if __name__ == "__main__":
    print("--- EvolutionMatrix 类使用示例 ---")

    Q0, Q_final, R = 2000.0, 4000.0, 0.4

    # NLO/NLL 演化 (物理输入 order=1, 重求和 m=1)
    evolver_nlo = EvolutionMatrix(order=0)
    print(evolver_nlo.get_evolution_operator(Q0, Q_final, R, 4))
    print(evolver_nlo.get_operator_fit(20, 0.6))
