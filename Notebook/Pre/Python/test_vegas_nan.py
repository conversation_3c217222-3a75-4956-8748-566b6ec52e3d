#!/usr/bin/env python3
"""
测试 VEGAS 中 nan 问题的简单脚本
"""

import numpy as np
import vegas

def simple_test_function(x):
    """一个简单的测试函数"""
    # 确保 x 是二维数组
    x = np.atleast_2d(x)
    # 返回一个简单的多维函数
    return np.exp(-np.sum(x**2, axis=1))

def constant_function(x):
    """返回常数的函数"""
    x = np.atleast_2d(x)
    return np.ones(x.shape[0])

def slightly_varying_function(x):
    """轻微变化的函数"""
    x = np.atleast_2d(x)
    return 1.0 + 0.1 * np.sum(x, axis=1)

print("测试1: 简单的高斯函数")
integ1 = vegas.Integrator([[0, 1], [0, 1], [0, 1], [0, 1]])
result1 = integ1(simple_test_function, nitn=10, neval=10000)
print(result1.summary())

print("\n" + "="*50)
print("测试2: 常数函数")
integ2 = vegas.Integrator([[0, 1], [0, 1], [0, 1], [0, 1]])
result2 = integ2(constant_function, nitn=10, neval=10000)
print(result2.summary())

print("\n" + "="*50)
print("测试3: 轻微变化的函数")
integ3 = vegas.Integrator([[0, 1], [0, 1], [0, 1], [0, 1]])
result3 = integ3(slightly_varying_function, nitn=10, neval=10000)
print(result3.summary())
