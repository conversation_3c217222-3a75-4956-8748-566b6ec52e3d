//////////////////////////////////////////////////////////////////////////////
//
// File to simulate process p p-> jj + X  with Jet Energy Correlator Measured ////
//
/////////////////////////////////////////////////////////////////////////////
#include "Pythia8/Pythia.h"

#include <algorithm>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <vector>

namespace fs = std::filesystem;
using namespace Pythia8;
using namespace std;

// wait on enter, for debugging.
inline void wait_on_enter() {
    std::string dummy;
    std::cout << "Enter to continue..." << std::endl;
    std::getline(std::cin, dummy);
}

int main(int argc, char* argv[]) {

    if (argc < 12) {
        cerr << "Usage: " << argv[0] << " [eCM] [channel] [ptlow] [ptupp] [pow1] [pow2] [chargeflag] [nEvent] [seed] [alphaS_MZ] [path]" << endl;
        return 1;
    }

    // Configuaration parameters
    const double ecom          = stod(argv[1]);   // machine energy
    const int    chflag        = stoi(argv[2]);   // channels
    const double elow          = stod(argv[3]);   // jet pt lower bound
    const double eupp          = stod(argv[4]);   // jet pt upper bound
    const double pow1          = stod(argv[5]);   // energy power z1^pow1
    const double pow2          = stod(argv[6]);   // energy power z2^pow2
    const int    chgflag       = stoi(argv[7]);   // all hadron ? track
    const int    nEvent        = stoi(argv[8]);   // number of events
    const string numofparallel = argv[9];         // seed
    const double alphaS_MZ     = stod(argv[10]);  // alpha_s at M_Z
    const string path          = argv[11];        // output path

    // Jet Parameters
    float  radius   = 0.4;
    double eta_pmax = 0.5;
    double eta_jmax = 0.5;
    double pTparMin = 0.15;        // 1.0 ;
    double pTjetMin = elow * 0.8;  /// 2.0;
    double dphi     = 2.0;

    // EEC histogram Parameters
    const int     nbin     = 100;
    const double  lnq2zmin = -4.0;
    const double  lnq2zmax = 1.0;
    const double  bw_z     = (lnq2zmax - lnq2zmin) / nbin;
    Hist          eeclogH("EmEnc: log plot", nbin, lnq2zmin, lnq2zmax);
    unsigned long jetCounter = 0;

    // Initialize Pythia
    Pythia        pythia;
    Event&        event        = pythia.event;
    Settings&     settings     = pythia.settings;
    ParticleData& particleData = pythia.particleData;

    // Set up pp beams and center of mass energy
    pythia.readString("Beams:frameType = 1");
    pythia.readString("Beams:idA = 2212");
    pythia.readString("Beams:idB = 2212");
    pythia.settings.parm("Beams:eCM", ecom);

    // Set up pp beams and center of mass energy
    pythia.readString("Beams:frameType = 1");
    pythia.readString("Beams:idA = 2212");
    pythia.readString("Beams:idB = 2212");
    pythia.settings.parm("Beams:eCM", ecom);

    // set alpha_s at M_Z, and two loop running for the process
    pythia.settings.parm("SigmaProcess:alphaSvalue", alphaS_MZ);
    pythia.readString("SigmaProcess:alphaSorder = 2");
    ////also set alpha_s, and two loop running and CMW scheme (so that it gets the two loop cusp) for the shower.
    pythia.settings.parm("TimeShower:alphaSvalue", alphaS_MZ);
    pythia.readString("TimeShower:alphaSorder = 2");
    pythia.readString("TimeShower:alphaSuseCMW = on");
    pythia.settings.parm("SpaceShower:alphaSvalue", alphaS_MZ);
    pythia.readString("SpaceShower:alphaSorder = 2");
    pythia.readString("SpaceShower:alphaSuseCMW = on");

    // Interaction mechanism.
    switch (chflag) {
    case 0:
        pythia.readString("HardQCD:all = on");
        pythia.readString("HardQCD:nQuarkNew = 5");
        break;
    case 1:
        pythia.readString("HardQCD:gg2qqbar = on");
        pythia.readString("HardQCD:nQuarkNew = 5");
        break;
    case 2:
        pythia.readString("HardQCD:gg2gg = on");
        break;
    case 3:
        pythia.readString("WeakBosonAndParton:qg2gmZq = on");
        break;
    case 4:
        pythia.readString("WeakBosonAndParton:qqbar2gmZg = on");
        break;
    default:
        cerr << "Invalid channel selection!" << endl;
        return 1;
    }

    string folder = to_string((int)elow) + "-" + to_string((int)eupp) + "GeV" + "_" + to_string((double)alphaS_MZ);
    // output file in folder
    string outPath;
    switch (chflag) {
    case 0:
        outPath = path + "/data/" + folder + "/" + to_string((int)ecom) + "GeV_ptlow_" + to_string((int)elow) + "GeV_ptupp_" + to_string((int)eupp) + "GeV_all_parton_" + to_string(chflag) + "_Charge_"
                  + to_string(chgflag) + "_E" + to_string((int)pow1) + "E" + to_string((int)pow2) + "C_run_" + numofparallel + "_log.txt";

        break;
    case 1:
    case 3:
        outPath = path + "/data/" + folder + "/" + to_string((int)ecom) + "GeV_ptlow_" + to_string((int)elow) + "GeV_ptupp_" + to_string((int)eupp) + "GeV_quark_parton_" + to_string(chflag)
                  + "_Charge_" + to_string(chgflag) + "_E" + to_string((int)pow1) + "E" + to_string((int)pow2) + "C_run_" + numofparallel + "_log.txt";
        break;
    case 2:
    case 4:
        outPath = path + "/data/" + folder + "/" + to_string((int)ecom) + "GeV_ptlow_" + to_string((int)elow) + "GeV_ptupp_" + to_string((int)eupp) + "GeV_gluon_parton_" + to_string(chflag)
                  + "_Charge_" + to_string(chgflag) + "_E" + to_string((int)pow1) + "E" + to_string((int)pow2) + "C_run_" + numofparallel + "_log.txt";
        break;
    default:
        cerr << "Invalid channel selection!" << endl;
        return 1;
    }

    try {
        fs::path directory_path = fs::path(outPath).parent_path();
        if (!directory_path.empty() && !fs::exists(directory_path)) {
            fs::create_directories(directory_path);
            cout << "Created directory: " << directory_path.string() << endl;
        }
    }
    catch (const fs::filesystem_error& e) {
        cerr << "Error creating directory: " << e.what() << endl;
        return 1;  // Exit if directory creation fails
    }

    ofstream file1(path + "/data/" + folder + "/q2z.txt", ios::trunc);  // save q2z bin
    for (int j = 1; j < nbin + 2; j++) {
        double vq2z = lnq2zmin + (j - 1) * bw_z;
        file1 << fixed << setprecision(6) << pow(10, vq2z) << endl;
    }
    file1.close();
    cout << "q2z bin saved to q2z.txt." << endl;
    cout << endl;

    // pythia.readString("PhaseSpace:mHatMin = 0.");
    // double pTcut = pTjetMin*0.8;
    pythia.settings.parm("PhaseSpace:pTHatMin", pTjetMin);

    // QED radiation off lepton not handled yet by the new procedure.
    pythia.readString("PDF:lepton = off");
    pythia.readString("TimeShower:QEDshowerByL = off");

    pythia.readString("PartonLevel:MPI = off");
    // pythia.readString("PartonLevel:Remnants = off");
    // pythia.readString("PartonLevel:ISR = off");
    // pythia.readString("PartonLevel:FSR = off");

    pythia.readString("HadronLevel:all = on");
    // pythia.readString("HadronLevel:Hadronize = off");
    // pythia.readString("HadronLevel:Decay = off");

    // make charged hadron not decay, so we can observe them in final-state
    // pi0: 111 pi+: 211 K+: 321 p: 2212 n: 2112
    // it also turn off the anti-particle of the above
    int notDecay[5] = { 111, 211, 321, 2212, 2112 };
    for (int iC = 0; iC < 5; ++iC) {
        particleData.mayDecay(notDecay[iC], false);
    }
    // turn off Z decay
    pythia.particleData.mayDecay(23, false);

    // print out information
    // pythia.readString("Next:numberCount = 0");
    pythia.readString("Next:numberShowInfo = 10");
    pythia.readString("Next:numberShowProcess = 10");
    pythia.readString("Next:numberShowEvent = 10");

    // use random seed
    // pythia.readString("Random:setSeed = on");
    // pythia.readString("Random:seed = 0");
    pythia.readString("Random:setSeed = on");
    pythia.readString(("Random:seed = " + numofparallel).c_str());

    // save data for every 50000 events created
    const int output_interval = 50000;

    SlowJet slowJet(-1, radius, pTjetMin, eta_jmax);

    pythia.init();

    for (int iEvent = 0; iEvent < nEvent; ++iEvent) {

        if (!pythia.next())
            continue;
        double weight = pythia.info.weight();

        for (const auto& p : event)
            ;

        slowJet.analyze(pythia.event);

        int nJet = slowJet.sizeJet();

        // demand at least jets
        int nLeadJet;
        switch (chflag) {
        case 0:
        case 1:
        case 2:
            nLeadJet = 2;
            break;
        case 3:
        case 4:
            nLeadJet = 1;
            break;
        default:
            return 1;
        }

        if (nJet < nLeadJet)
            continue;

        // leading 2-jet requirements, follow cms
        double phijet1 = slowJet.p(0).phi();
        double phijet2;
        if (nLeadJet == 2)
            phijet2 = slowJet.p(1).phi();
        if (nLeadJet == 1)
            phijet2 = event[5].phi();

        if (abs(phijet1 - phijet2) < dphi)
            continue;  // close to b2b configuration

        // loop over leading jets
        for (int iJet = 0; iJet < nLeadJet; iJet++) {
            double EJet   = slowJet.p(iJet).e();
            double etaJet = slowJet.p(iJet).eta();
            double pTJet  = slowJet.p(iJet).pT();

            if (pTJet < elow || pTJet > eupp)
                continue;
            if (abs(etaJet) > eta_jmax)
                continue;

            ++jetCounter;

            vector<int> constituents = slowJet.constituents(iJet);

            for (int iPart = 0; iPart < slowJet.multiplicity(iJet); iPart++) {

                if (iPart >= constituents.size())
                    continue;

                double ei      = event[constituents[iPart]].e();
                double pti     = event[constituents[iPart]].pT();
                double etai    = event[constituents[iPart]].eta();
                double phii    = event[constituents[iPart]].phi();
                double chargei = event[constituents[iPart]].charge();

                double zi = ei / EJet / EJet;

                if (pti < pTparMin)
                    continue;

                // no counter term included
                for (int jPart = iPart + 1; jPart < constituents.size(); jPart++) {

                    if (jPart >= constituents.size())
                        continue;

                    double ej      = event[constituents[jPart]].e();
                    double ptj     = event[constituents[jPart]].pT();
                    double etaj    = event[constituents[jPart]].eta();
                    double phij    = event[constituents[jPart]].phi();
                    double chargej = event[constituents[jPart]].charge();

                    double zj = ej / EJet / EJet;

                    if (ptj < pTparMin)
                        continue;

                    double zizj   = .5 * (pow(zi, pow1) * pow(zj, pow2) + pow(zi, pow2) * pow(zj, pow1));
                    double RLij   = (etai - etaj) * (etai - etaj) + (phii - phij) * (phii - phij);
                    double q2zeec = EJet * EJet * RLij;

                    // factor of 2 account for ji configuration
                    if (chgflag == 0 && event[constituents[iPart]].isHadron() && event[constituents[jPart]].isHadron()) {
                        eeclogH.fill(log10(q2zeec), 2.0 * zizj * weight);
                    }
                    else if (chgflag == 1
                             && (event[constituents[iPart]].isCharged() && event[constituents[jPart]].isCharged() && event[constituents[iPart]].isHadron() && event[constituents[jPart]].isHadron())) {
                        eeclogH.fill(log10(q2zeec), abs(chargei * chargej) * 2.0 * zizj * weight);
                    }
                }
            }

            // save temporary data
            if ((iEvent + 1) % output_interval == 0 || (iEvent == nEvent - 1)) {
                double current_jetCounter = jetCounter;
                double current_normFactor = current_jetCounter > 0 ? 1.0 / (bw_z * current_jetCounter) : 0.0;

                ofstream file2(outPath.c_str(), ios::trunc);

                for (int jt = 1; jt < nbin + 1; jt++) {
                    file2 << fixed << setprecision(8) << eeclogH.getBinContent(jt) * current_normFactor << endl;
                }
                file2.close();
            }
        }
    }

    pythia.stat();
    cout << eeclogH;
    double sigmaNorm = pythia.info.sigmaGen() / pythia.info.weightSum() * 1.0e12;  // total cross section in fb
    // double Norm = pythia.info.weightSum();
    double Norm       = jetCounter;  // JetSig.getBinContent(1);
    double normFactor = jetCounter > 0 ? 1.0 / (bw_z * jetCounter) : 0.0;

    // print out basic information
    cout << "sigmaGen: " << pythia.info.sigmaGen() * 1.0e9 << " pb "
         << "  weightSum: " << pythia.info.weightSum() << endl;
    cout << "lnq2z bin width: " << bw_z << endl;
    cout << "jet counts: " << Norm << endl;

    cout << endl;
    cout << endl;

    ofstream file2(outPath.c_str(), ios::trunc);
    for (int j = 1; j < nbin + 1; j++) {
        file2 << fixed << setprecision(8) << eeclogH.getBinContent(j) * normFactor << endl;
    }
    file2.close();

    cout << "========= Done! All data saved! ==========" << endl;

    return 0;
}
